import Checkbox from "@/components/ui/Checkbox";
import PrimaryButton from "@/components/ui/PrimaryButton";
import { COLORS } from "@/constants";
import { router } from "expo-router";
import React, { useState } from "react";
import { Image, StyleSheet, Text, TextInput, View } from "react-native";
import {
  KeyboardAwareScrollView,
  KeyboardToolbar,
} from "react-native-keyboard-controller";

const Login = () => {
  const [checked, setChecked] = useState(false);
  const setCheckedHandler = () => {
    setChecked((prev) => !prev);
  };
  return (
    <View style={styles.container}>
      <KeyboardAwareScrollView
        bottomOffset={62}
        contentContainerStyle={{ flexGrow: 1 }}
      >
        <View style={styles.titleContainer}>
          <Image
            source={require("@/assets/images/hiforce-logo.png")}
            style={{ width: "60%", resizeMode: "contain" }}
          />
        </View>
        <View style={styles.contentContainer}>
          <Text style={styles.titleText}>Welcome!</Text>
          <View>
            <TextInput
              placeholder="Email address"
              placeholderTextColor={COLORS.white}
              style={styles.textInput}
            />
            <TextInput
              placeholder="Password"
              placeholderTextColor={COLORS.white}
              style={styles.textInput}
            />
          </View>
          <View style={styles.checkboxContainer}>
            <Checkbox checked={checked} setChecked={setCheckedHandler} />
            <Text style={styles.checkboxLabel}>Remember Password</Text>
          </View>
          <PrimaryButton
            label="Log in"
            handleSubmit={() => router.replace("/(tabs)")}
          />
        </View>
      </KeyboardAwareScrollView>
      <KeyboardToolbar />
    </View>
  );
};

export default Login;

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: COLORS.primary },
  titleContainer: {
    height: "25%",
    justifyContent: "center",
    alignItems: "center",
  },
  contentContainer: {
    backgroundColor: COLORS.darkGray,
    gap: 16,
    padding: 16,
    flex: 1,
    borderTopRightRadius: 30,
    borderTopLeftRadius: 30,
  },

  titleText: { fontSize: 32, marginTop: 20 },
  textInput: {
    backgroundColor: COLORS.darkGray,
    width: "auto",
    flexGrow: 1,
    flexShrink: 1,
    fontSize: 16,
    height: 60,
    borderWidth: 1,
    borderRadius: 8,
    borderColor: COLORS.white,
    padding: 8,
    marginBottom: 20,
    color: COLORS.white,
  },
  checkboxContainer: {
    // backgroundColor: "olive",
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 20,
  },
  checkboxLabel: {
    fontSize: 16,
    marginLeft: 10,
    color: COLORS.white,
  },
});

import PrimaryButton from "@/components/ui/PrimaryButton";
import { COLORS } from "@/constants";
import Entypo from "@expo/vector-icons/Entypo";
import { router } from "expo-router";
import React from "react";
import { Pressable, StyleSheet, Text, View } from "react-native";

const index = () => {
  return (
    <View style={styles.container}>
      <View style={styles.titleContainer}>
        <Text
          style={{
            fontSize: 24,
            fontWeight: "bold",
            color: COLORS.black,
            textAlign: "center",
          }}
        >
          Hi-Force {"\n"}Hydraulic Tools
        </Text>
      </View>
      <View style={styles.contentContainer}>
        <PrimaryButton label="Get Started" handleSubmit={() => {}} />
        <View style={styles.loginContainer}>
          <Text style={styles.brandName}>Hi-Force Group </Text>
          <Pressable
            style={styles.button}
            onPress={() => {
              router.push("/(auth)/login");
            }}
          >
            <Text style={styles.buttonText}>Log in</Text>
          </Pressable>
          <Entypo name="info-with-circle" size={24} color={COLORS.white} />
        </View>
      </View>
    </View>
  );
};

export default index;

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: COLORS.primary },
  titleContainer: {
    height: "25%",
    justifyContent: "center",
    alignItems: "center",
  },
  contentContainer: {
    padding: 20,
    backgroundColor: COLORS.darkGray,
    flex: 1,
    justifyContent: "center",
    borderTopRightRadius: 30,
    borderTopLeftRadius: 30,
  },
  loginContainer: {
    flexDirection: "row",
    justifyContent: "center",
    marginTop: 30,
  },
  brandName: {
    fontSize: 20,
    color: COLORS.white,
  },
  button: { marginRight: 10 },
  buttonText: {
    fontSize: 20,
    color: COLORS.white,
    textDecorationLine: "underline",
    fontWeight: "bold",
  },
});

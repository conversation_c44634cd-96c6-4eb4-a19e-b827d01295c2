import { COLORS } from "@/constants";
import { router } from "expo-router";
import { useEffect } from "react";
import { Text, View } from "react-native";

export default function Index() {
  useEffect(() => {
    const timer = setTimeout(() => {
      router.replace("/(auth)");
    }, 0);
    return () => clearTimeout(timer);
  }, []);

  return (
    <View
      style={{
        flex: 1,
        justifyContent: "center",
        alignItems: "center",
      }}
    >
      <Text
        style={{
          fontSize: 24,
          fontWeight: "bold",
          color: COLORS.black,
          textAlign: "center",
        }}
      >
        Hi-Force {"\n"}Hydraulic Tools
      </Text>
    </View>
  );
}

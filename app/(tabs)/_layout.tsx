import AppHeader from "@/components/ui/AppHeader";
import FontAwesome5 from "@expo/vector-icons/FontAwesome5";
import FontAwesome6 from "@expo/vector-icons/FontAwesome6";
import MaterialCommunityIcons from "@expo/vector-icons/MaterialCommunityIcons";
import MaterialIcons from "@expo/vector-icons/MaterialIcons";
import { router, Tabs } from "expo-router";
import React from "react";

const TabLayout = () => {
  return (
    <Tabs>
      <Tabs.Screen
        name="index"
        options={{
          title: "Workspaces",
          header: (props) => <AppHeader {...props} title="Workspaces" />,
          tabBarIcon: ({ color }) => (
            <MaterialIcons name="workspaces-outline" size={24} color={color} />
          ),
        }}
        listeners={{
          tabPress: (e) => {
            e.preventDefault();
            router.replace("/");
          },
        }}
      />
      <Tabs.Screen
        name="my_content"
        options={{
          title: "My Content",
          header: (props) => <AppHeader {...props} title="My Content" />,
          tabBarIcon: ({ color }) => (
            <FontAwesome6 name="folder-blank" size={24} color={color} />
          ),
        }}
        listeners={{
          tabPress: (e) => {
            e.preventDefault();
            router.replace("/my_content");
          },
        }}
      />

      <Tabs.Screen
        name="account"
        options={{
          title: "Account",
          header: (props) => <AppHeader {...props} title="Account" />,
          tabBarIcon: ({ color }) => (
            <FontAwesome5 name="user-circle" size={24} color={color} />
          ),
        }}
        listeners={{
          tabPress: (e) => {
            e.preventDefault();
            router.replace("/account");
          },
        }}
      />

      <Tabs.Screen
        name="shares"
        options={{
          title: "Shares",
          header: (props) => <AppHeader {...props} title="Shares" />,
          tabBarIcon({ color }) {
            return (
              <MaterialCommunityIcons
                name="email-outline"
                size={24}
                color={color}
              />
            );
          },
        }}
        listeners={{
          tabPress: (e) => {
            e.preventDefault();
            router.replace("/shares");
          },
        }}
      />
    </Tabs>
  );
};

export default TabLayout;

import FontAwesome5 from "@expo/vector-icons/FontAwesome5";
import FontAwesome6 from "@expo/vector-icons/FontAwesome6";
import MaterialCommunityIcons from "@expo/vector-icons/MaterialCommunityIcons";
import MaterialIcons from "@expo/vector-icons/MaterialIcons";
import { Tabs } from "expo-router";
import React from "react";

const TabLayout = () => {
  return (
    <Tabs>
      <Tabs.Screen
        name="index"
        options={{
          title: "Workspaces",
          headerShown: false,
          tabBarIcon: ({ color }) => (
            <MaterialIcons name="workspaces-outline" size={24} color={color} />
          ),
        }}
      />
      <Tabs.Screen
        name="my_content"
        options={{
          title: "My Content",
          headerShown: false,
          tabBarIcon: ({ color }) => (
            <FontAwesome6 name="folder-blank" size={24} color={color} />
          ),
        }}
      />
      <Tabs.Screen
        name="shares"
        options={{
          title: "Shares",
          headerShown: false,
          tabBarIcon({ color }) {
            return (
              <MaterialCommunityIcons
                name="email-outline"
                size={24}
                color={color}
              />
            );
          },
        }}
      />
      <Tabs.Screen
        name="account"
        options={{
          title: "Account",
          headerShown: false,
          tabBarIcon: ({ color }) => (
            <FontAwesome5 name="user-circle" size={24} color={color} />
          ),
        }}
      />
    </Tabs>
  );
};

export default TabLayout;

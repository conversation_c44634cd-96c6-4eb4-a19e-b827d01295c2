import { COLORS } from "@/constants";
import React from "react";
import { StyleSheet, Text, TouchableOpacity } from "react-native";

interface PrimaryButtonProps {
  label: string;
  handleSubmit: () => void;
}

const PrimaryButton = ({ label, handleSubmit }: PrimaryButtonProps) => {
  return (
    <TouchableOpacity onPress={handleSubmit} style={styles.button}>
      <Text style={styles.buttonText}>{label}</Text>
    </TouchableOpacity>
  );
};

export default PrimaryButton;

const styles = StyleSheet.create({
  button: {
    backgroundColor: COLORS.secondary,
    paddingVertical: 16,
    borderRadius: 16,
  },
  buttonText: {
    color: COLORS.white,
    // fontWeight: "bold",
    fontSize: 20,
    textAlign: "center",
  },
});

import { COLORS } from "@/constants";
import AntDesign from "@expo/vector-icons/AntDesign";
import { router } from "expo-router";
import React from "react";
import { Pressable, StyleSheet, Text, View } from "react-native";
import { useSafeAreaInsets } from "react-native-safe-area-context";

const AppHeader = (props: any) => {
  const insets = useSafeAreaInsets();

  return (
    <View
      style={[
        styles.headerContainer,
        { paddingTop: insets.top, backgroundColor: COLORS.secondary },
      ]}
    >
      <Pressable
        style={styles.backButton}
        onPress={() => {
          props.navigation.canGoBack() && router.back();
        }}
      >
        {props.navigation.canGoBack() && (
          <AntDesign name="arrowleft" size={24} color={COLORS.white} />
        )}
        <Text style={[styles.backButtonText, { color: COLORS.white }]}>
          {props.title || "Back"}
        </Text>
      </Pressable>
    </View>
  );
};

export default AppHeader;

const styles = StyleSheet.create({
  headerContainer: {
    paddingHorizontal: 10,
    paddingVertical: 20,
  },
  backButton: {
    // backgroundColor: "cyan",
    height: 24,
    // flex: 1,
    flexDirection: "row",
    alignItems: "center",
  },
  backButtonText: {
    fontSize: 16,
    marginLeft: 5,
  },
});

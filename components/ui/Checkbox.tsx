import { COLORS } from "@/constants";
import Fontisto from "@expo/vector-icons/Fontisto";
import React from "react";
import { Pressable, StyleSheet } from "react-native";

interface CheckboxProps {
  checked?: boolean;
  setChecked: () => void;
}

const Checkbox = ({ checked = false, setChecked }: CheckboxProps) => {
  return (
    <Pressable onPress={setChecked}>
      {checked ? (
        <Fontisto name="checkbox-active" size={24} color={COLORS.white} />
      ) : (
        <Fontisto name="checkbox-passive" size={24} color={COLORS.white} />
      )}
    </Pressable>
  );
};

export default Checkbox;

const styles = StyleSheet.create({});

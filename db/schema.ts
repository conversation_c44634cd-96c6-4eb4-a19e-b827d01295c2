import { index, integer, sqliteTable, text } from "drizzle-orm/sqlite-core";

export const foldersTable = sqliteTable(
  "folders",
  {
    id: integer("id").primaryKey(), // server-provided; no autoincrement assumed
    name: text("name").notNull(),
    description: text("description"), // nullable
    hideLabelInWorkspace: integer("hide_label_in_workspace", {
      mode: "boolean",
    })
      .notNull()
      .default(false),
    folderIconPath: text("folder_icon_path"), // nullable
    workspaceId: integer("workspace_id").notNull(),
    folderId: integer("folder_id").notNull(),
    sequenceNumber: integer("sequence_number").notNull(),
    isActive: integer("is_active", { mode: "boolean" }).notNull().default(true),
  },
  (t) => ({
    workspaceIdx: index("idx_folders_workspace_id").on(t.workspaceId),
    folderIdx: index("idx_folders_folder_id").on(t.folderId),
    sequenceIdx: index("idx_folders_sequence").on(
      t.workspaceId,
      t.sequenceNumber
    ),
  })
);

export type Folder = typeof foldersTable.$inferSelect;
export type NewFolder = typeof foldersTable.$inferInsert;
